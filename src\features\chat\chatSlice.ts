import { Document, ChatMessage, ChatSession } from '@/types/chat';
import { documentApi, sessionApi } from '@/services/api';
import { chatApi } from '@/services/chatApi';

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { createSelector } from '@reduxjs/toolkit';

import { RootState } from '../../app/store';

// API response interfaces
interface ApiSessionResponse {
  session_id: string;
  name: string;
  last_message_at?: string;
  created_at: string;
  document_ids?: string[];
}

interface ApiDocumentResponse {
  id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  created_at: string;
  url?: string;
  file_id?: string;
  status?: string;
  s3_key?: string;
}

// Define chat state
export interface ChatState {
  chatSessions: ChatSession[];
  chatSessionDocuments: Record<string, Document[]>;
  currentChatSession: ChatSession | null;
  currentDocument: Document | null;
  messages: ChatMessage[];
  isLoading: boolean;
  isLoadingMessages: boolean;
  isSendingMessage: boolean;
  error: string | null;
}

// Helper functions for localStorage
const getStoredSessionId = (): string | null => {
  try {
    return localStorage.getItem('anydocai_current_session_id');
  } catch {
    return null;
  }
};

const setStoredSessionId = (sessionId: string | null): void => {
  try {
    if (sessionId) {
      localStorage.setItem('anydocai_current_session_id', sessionId);
    } else {
      localStorage.removeItem('anydocai_current_session_id');
    }
  } catch {
    // Ignore localStorage errors
  }
};

const getStoredDocumentId = (): string | null => {
  try {
    return localStorage.getItem('anydocai_current_document_id');
  } catch {
    return null;
  }
};

const setStoredDocumentId = (documentId: string | null): void => {
  try {
    if (documentId) {
      localStorage.setItem('anydocai_current_document_id', documentId);
    } else {
      localStorage.removeItem('anydocai_current_document_id');
    }
  } catch {
    // Ignore localStorage errors
  }
};

// Initial state
const initialState: ChatState = {
  chatSessions: [],
  chatSessionDocuments: {},
  currentChatSession: null,
  currentDocument: null,
  messages: [],
  isLoading: false,
  isLoadingMessages: false,
  isSendingMessage: false,
  error: null,
};

// Async thunks
export const fetchChatSessions = createAsyncThunk(
  'chat/fetchChatSessions',
  async (_, { rejectWithValue }) => {
    try {
      const response = await sessionApi.listSessions();

      // Map the API response to our ChatSession type
      const sessions = response.data.sessions.map((session: ApiSessionResponse) => ({
        id: session.session_id,
        name: session.name,
        lastMessageTime: session.last_message_at || session.created_at,
        documentIds: session.document_ids || []
      }));

      // Sort sessions by last_message_at (latest first)
      sessions.sort((a: ChatSession, b: ChatSession) => new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime());

      return sessions as ChatSession[];
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch chat sessions');
    }
  }
);

export const fetchSessionDocuments = createAsyncThunk(
  'chat/fetchSessionDocuments',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      // Guard clause to prevent API calls with undefined sessionId
      if (!sessionId || sessionId === 'undefined') {
        return rejectWithValue('Invalid session ID');
      }

      const response = await documentApi.getSessionDocumentDetails(sessionId);

      // Map the API response to our Document type
      const documents = response.data.documents.map((doc: ApiDocumentResponse) => ({
        id: doc.id,
        name: doc.file_name,
        type: doc.file_type,
        size: `${(doc.file_size / (1024 * 1024)).toFixed(1)} MB`,
        dateUploaded: new Date(doc.created_at).toISOString().split('T')[0],
        content: '',
        url: doc.url,
        file_id: doc.file_id,
        status: doc.status,
        s3_key: doc.s3_key,
        created_at: doc.created_at
      }));

      // Sort documents by created_at (latest first)
      documents.sort((a: Document, b: Document) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime());

      return {
        sessionId,
        documents
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch session documents');
    }
  }
);

export const fetchMessages = createAsyncThunk(
  'chat/fetchMessages',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      // Guard clause to prevent API calls with undefined sessionId
      if (!sessionId || sessionId === 'undefined') {
        return rejectWithValue('Invalid session ID');
      }

      const response = await chatApi.getMessages(sessionId);
      // The API returns messages directly in the response, not nested under data
      return response.messages as ChatMessage[];
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch messages');
    }
  }
);

export const createChatSession = createAsyncThunk(
  'chat/createChatSession',
  async (name: string) => {
    try {
      const response = await sessionApi.createSession(name);

      // Check if we have a valid response
      if (response && response.data && response.data.session) {
        return response.data.session as ChatSession;
      }

      // If API response is not in expected format, create a mock session
      const mockSession: ChatSession = {
        id: Date.now().toString(),
        name: name,
        lastMessageTime: new Date().toISOString(),
        documentIds: []
      };

      return mockSession;
    } catch (error) {
      // If API is not available, create a mock session
      console.warn('API not available, creating mock session:', error);

      const mockSession: ChatSession = {
        id: Date.now().toString(),
        name: name,
        lastMessageTime: new Date().toISOString(),
        documentIds: []
      };

      return mockSession;
    }
  }
);

export const updateChatSessionAPI = createAsyncThunk(
  'chat/updateChatSessionAPI',
  async ({ sessionId, name }: { sessionId: string; name: string }, { rejectWithValue }) => {
    try {
      const response = await sessionApi.updateSession(sessionId, name);

      // Map the API response to our ChatSession type
      const updatedSession = {
        id: response.data.session_id || sessionId,
        name: response.data.name || name,
        lastMessageTime: response.data.last_message_at || response.data.updated_at || new Date().toISOString(),
        documentIds: response.data.document_ids || []
      };

      return updatedSession as ChatSession;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update chat session');
    }
  }
);

export const deleteChatSessionAPI = createAsyncThunk(
  'chat/deleteChatSessionAPI',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      await sessionApi.deleteSession(sessionId);
      return sessionId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete chat session');
    }
  }
);

export const uploadDocumentToSession = createAsyncThunk(
  'chat/uploadDocumentToSession',
  async ({ file, sessionId }: { file: File; sessionId: string }, { rejectWithValue }) => {
    try {
      const response = await documentApi.uploadDocumentToSession(file, sessionId);

      // Map the API response to our Document type
      const document = {
        id: response.data.document_id || response.data.id,
        name: response.data.file_name || file.name,
        type: response.data.file_type || file.type,
        size: response.data.file_size || file.size.toString(),
        dateUploaded: response.data.created_at || new Date().toISOString(),
        file_id: response.data.file_id,
        status: response.data.status,
        s3_key: response.data.s3_key,
        url: response.data.url
      };

      return { sessionId, document };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to upload document');
    }
  }
);

// Define the actual API response type for sendMessage
interface SendMessageResponse {
  message?: string;
  data?: {
    message?: string;
    metadata?: Record<string, unknown>;
  };
  metadata?: Record<string, unknown>;
}

export const sendMessage = createAsyncThunk<SendMessageResponse, {
  sessionId: string;
  message: string;
  chatHistory?: ChatMessage[];
  useAgent?: boolean;
}>(
  'chat/sendMessage',
  async ({
    sessionId,
    message,
    chatHistory = [],
    useAgent = false
  }, { rejectWithValue }) => {
    try {
      // Limit chat history to last 10 messages to prevent large payloads and duplication
      const recentHistory = chatHistory.slice(-10);

      // Convert ChatMessage[] to ChatHistoryMessage[] format expected by API
      const formattedChatHistory = recentHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Use the correct chatApi from chatApi.ts which has the proper endpoint
      const response = await chatApi.sendMessage(sessionId, {
        message,
        chat_history: formattedChatHistory,
        use_agent: useAgent
      });

      return response as SendMessageResponse; // Return the ChatResponse data with proper typing
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to send message');
    }
  }
);

// Combined thunk to initialize chat: fetch sessions, select latest, and fetch its documents
export const initializeChat = createAsyncThunk(
  'chat/initializeChat',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      // First, fetch all chat sessions
      const sessionsResult = await dispatch(fetchChatSessions()).unwrap();

      if (sessionsResult.length > 0) {
        // Get the latest session (first in sorted array)
        const latestSession = sessionsResult[0];

        // Fetch documents for the latest session
        await dispatch(fetchSessionDocuments(latestSession.id)).unwrap();

        // Fetch messages for the latest session
        await dispatch(fetchMessages(latestSession.id)).unwrap();

        return {
          selectedSessionId: latestSession.id,
          sessionsCount: sessionsResult.length
        };
      }

      return {
        selectedSessionId: null,
        sessionsCount: 0
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to initialize chat');
    }
  }
);

// Chat slice
const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    // Set current chat session
    setCurrentChatSession: (state, action: PayloadAction<ChatSession>) => {
      state.currentChatSession = action.payload;
      setStoredSessionId(action.payload.id);
      // Clear current document when switching sessions
      state.currentDocument = null;
      setStoredDocumentId(null);
      // Clear messages when switching sessions (they will be fetched for the new session)
      state.messages = [];
    },

    // Set current document
    setCurrentDocument: (state, action: PayloadAction<Document | null>) => {
      state.currentDocument = action.payload;
      setStoredDocumentId(action.payload?.id || null);
    },

    // Set chat sessions
    setChatSessions: (state, action: PayloadAction<ChatSession[]>) => {
      state.chatSessions = action.payload;
    },

    // Add a chat session
    addChatSession: (state, action: PayloadAction<ChatSession>) => {
      state.chatSessions = [action.payload, ...state.chatSessions];
      state.currentChatSession = action.payload;
      setStoredSessionId(action.payload.id);
      // Clear current document when creating new session
      state.currentDocument = null;
      setStoredDocumentId(null);
    },

    // Update a chat session
    updateChatSession: (state, action: PayloadAction<ChatSession>) => {
      state.chatSessions = state.chatSessions.map(session =>
        session.id === action.payload.id ? action.payload : session
      );

      if (state.currentChatSession?.id === action.payload.id) {
        state.currentChatSession = action.payload;
      }
    },

    // Delete a chat session
    deleteChatSession: (state, action: PayloadAction<string>) => {
      state.chatSessions = state.chatSessions.filter(session => session.id !== action.payload);

      if (state.currentChatSession?.id === action.payload) {
        const newCurrentSession = state.chatSessions.length > 0 ? state.chatSessions[0] : null;
        state.currentChatSession = newCurrentSession;
        setStoredSessionId(newCurrentSession?.id || null);
        // Clear current document when deleting current session
        state.currentDocument = null;
        setStoredDocumentId(null);
      }
    },

    // Set documents for a session
    setSessionDocuments: (state, action: PayloadAction<{ sessionId: string; documents: Document[] }>) => {
      state.chatSessionDocuments[action.payload.sessionId] = action.payload.documents;
    },

    // Add a document to a session
    addDocumentToSession: (state, action: PayloadAction<{ sessionId: string; document: Document }>) => {
      const { sessionId, document } = action.payload;

      // Add document to session documents
      if (!state.chatSessionDocuments[sessionId]) {
        state.chatSessionDocuments[sessionId] = [];
      }

      state.chatSessionDocuments[sessionId] = [...state.chatSessionDocuments[sessionId], document];

      // Update session documentIds
      const session = state.chatSessions.find(s => s.id === sessionId);
      if (session) {
        const updatedSession = {
          ...session,
          documentIds: [...session.documentIds, document.id]
        };

        state.chatSessions = state.chatSessions.map(s =>
          s.id === sessionId ? updatedSession : s
        );

        if (state.currentChatSession?.id === sessionId) {
          state.currentChatSession = updatedSession;
        }
      }
    },

    // Remove a document from a session
    removeDocumentFromSession: (state, action: PayloadAction<{ sessionId: string; documentId: string }>) => {
      const { sessionId, documentId } = action.payload;

      // Remove document from session documents
      if (state.chatSessionDocuments[sessionId]) {
        state.chatSessionDocuments[sessionId] = state.chatSessionDocuments[sessionId].filter(
          doc => doc.id !== documentId
        );
      }

      // Update session documentIds
      const session = state.chatSessions.find(s => s.id === sessionId);
      if (session) {
        const updatedSession = {
          ...session,
          documentIds: session.documentIds.filter(id => id !== documentId)
        };

        state.chatSessions = state.chatSessions.map(s =>
          s.id === sessionId ? updatedSession : s
        );

        if (state.currentChatSession?.id === sessionId) {
          state.currentChatSession = updatedSession;
        }
      }

      // If current document is being removed, set it to null
      if (state.currentDocument?.id === documentId) {
        state.currentDocument = null;
      }
    },

    // Set messages
    setMessages: (state, action: PayloadAction<ChatMessage[]>) => {
      state.messages = action.payload;
    },

    // Add a message
    addMessage: (state, action: PayloadAction<ChatMessage>) => {
      state.messages = [...state.messages, action.payload];
    },

    // Clear messages
    clearMessages: (state) => {
      state.messages = [];
    },

    // Clear chat state
    clearChatState: (state) => {
      state.chatSessions = [];
      state.chatSessionDocuments = {};
      state.currentChatSession = null;
      state.currentDocument = null;
      state.messages = [];
      // Clear localStorage as well
      setStoredSessionId(null);
      setStoredDocumentId(null);
    },
  },
  extraReducers: (builder) => {
    // fetchChatSessions
    builder
      .addCase(fetchChatSessions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchChatSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.chatSessions = action.payload;

        // Try to restore the previously selected session from localStorage
        const storedSessionId = getStoredSessionId();
        let sessionToSelect = null;

        if (storedSessionId) {
          // Find the stored session in the fetched sessions
          sessionToSelect = action.payload.find(session => session.id === storedSessionId);
        }

        // If no stored session found or stored session doesn't exist, use the latest session
        if (!sessionToSelect && action.payload.length > 0) {
          sessionToSelect = action.payload[0];
        }

        // Only set current chat session if none is currently selected
        if (!state.currentChatSession && sessionToSelect) {
          state.currentChatSession = sessionToSelect;
          setStoredSessionId(sessionToSelect.id);
        }
      })
      .addCase(fetchChatSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // fetchSessionDocuments
    builder
      .addCase(fetchSessionDocuments.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSessionDocuments.fulfilled, (state, action) => {
        state.isLoading = false;
        state.chatSessionDocuments[action.payload.sessionId] = action.payload.documents;

        // Only set current document if this is for the current session and documents are available
        if (action.payload.documents.length > 0 && state.currentChatSession?.id === action.payload.sessionId) {
          // Try to restore the previously selected document from localStorage
          const storedDocumentId = getStoredDocumentId();
          let documentToSelect = null;

          if (storedDocumentId) {
            // Find the stored document in the fetched documents
            documentToSelect = action.payload.documents.find((doc: Document) => doc.id === storedDocumentId);
          }

          // If no stored document found or stored document doesn't exist, use the latest document
          if (!documentToSelect) {
            documentToSelect = action.payload.documents[0];
          }

          // Only set current document if none is currently selected
          if (!state.currentDocument && documentToSelect) {
            state.currentDocument = documentToSelect;
            setStoredDocumentId(documentToSelect.id);
          }
        }
      })
      .addCase(fetchSessionDocuments.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // fetchMessages
    builder
      .addCase(fetchMessages.pending, (state) => {
        state.isLoadingMessages = true;
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.isLoadingMessages = false;
        state.messages = action.payload;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.isLoadingMessages = false;
        state.error = action.payload as string;
      });

    // createChatSession
    builder
      .addCase(createChatSession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createChatSession.fulfilled, (state, action) => {
        state.isLoading = false;
        const newSession = action.payload;
        if (newSession && newSession.id) {
          state.chatSessions = [newSession, ...state.chatSessions];
          state.currentChatSession = newSession;
          setStoredSessionId(newSession.id);
          // Clear current document when creating new session
          state.currentDocument = null;
          setStoredDocumentId(null);
        }
      })
      .addCase(createChatSession.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // sendMessage
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isSendingMessage = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isSendingMessage = false;
        // For WebSocket streaming, the response will come via WebSocket
        // For non-streaming, the response should contain the assistant's message
        // We'll handle adding the user message separately in the component
        const payload = action.payload;
        if (payload && (payload.message || payload.data?.message)) {
          // Ensure assistant message has a timestamp that's after the user message
          const now = new Date();
          now.setMilliseconds(now.getMilliseconds() + 1); // Add 1ms to ensure it comes after user message

          const assistantMessage: ChatMessage = {
            id: `assistant-${Date.now()}`,
            role: 'assistant',
            content: payload.message || payload.data?.message || '',
            timestamp: now.toISOString(),
            metadata: payload.metadata || payload.data?.metadata || {}
          };
          state.messages = [...state.messages, assistantMessage];
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isSendingMessage = false;
        state.error = action.payload as string;
      });

    // updateChatSessionAPI
    builder
      .addCase(updateChatSessionAPI.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateChatSessionAPI.fulfilled, (state, action) => {
        state.isLoading = false;
        state.chatSessions = state.chatSessions.map(session =>
          session.id === action.payload.id ? action.payload : session
        );
        if (state.currentChatSession?.id === action.payload.id) {
          state.currentChatSession = action.payload;
        }
      })
      .addCase(updateChatSessionAPI.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // deleteChatSessionAPI
    builder
      .addCase(deleteChatSessionAPI.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteChatSessionAPI.fulfilled, (state, action) => {
        state.isLoading = false;
        const sessionId = action.payload;
        state.chatSessions = state.chatSessions.filter(session => session.id !== sessionId);

        if (state.currentChatSession?.id === sessionId) {
          const newCurrentSession = state.chatSessions.length > 0 ? state.chatSessions[0] : null;
          state.currentChatSession = newCurrentSession;
          setStoredSessionId(newCurrentSession?.id || null);
          state.currentDocument = null;
          setStoredDocumentId(null);
        }
      })
      .addCase(deleteChatSessionAPI.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // uploadDocumentToSession
    builder
      .addCase(uploadDocumentToSession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(uploadDocumentToSession.fulfilled, (state, action) => {
        state.isLoading = false;
        const { sessionId, document } = action.payload;

        // Add document to session documents
        if (!state.chatSessionDocuments[sessionId]) {
          state.chatSessionDocuments[sessionId] = [];
        }
        state.chatSessionDocuments[sessionId] = [...state.chatSessionDocuments[sessionId], document];

        // Update session documentIds
        const session = state.chatSessions.find(s => s.id === sessionId);
        if (session) {
          const updatedSession = {
            ...session,
            documentIds: [...session.documentIds, document.id]
          };

          state.chatSessions = state.chatSessions.map(s =>
            s.id === sessionId ? updatedSession : s
          );

          if (state.currentChatSession?.id === sessionId) {
            state.currentChatSession = updatedSession;
          }
        }

        // Set as current document if this is for the current session
        if (state.currentChatSession?.id === sessionId) {
          state.currentDocument = document;
          setStoredDocumentId(document.id);
        }
      })
      .addCase(uploadDocumentToSession.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  setCurrentChatSession,
  setCurrentDocument,
  setChatSessions,
  addChatSession,
  updateChatSession,
  deleteChatSession,
  setSessionDocuments,
  addDocumentToSession,
  removeDocumentFromSession,
  setMessages,
  addMessage,
  clearMessages,
  clearChatState,
} = chatSlice.actions;

// Export selectors
export const selectCurrentChatSession = (state: RootState) => state.chat.currentChatSession;
export const selectCurrentDocument = (state: RootState) => state.chat.currentDocument;
export const selectMessages = (state: RootState) => state.chat.messages;

// Memoized selectors to prevent unnecessary rerenders
export const selectChatSessions = createSelector(
  [(state: RootState) => state.chat.chatSessions],
  (chatSessions) => chatSessions.filter(session => session && session.id)
);

export const selectSessionDocuments = createSelector(
  [(state: RootState) => state.chat.currentChatSession?.id, (state: RootState) => state.chat.chatSessionDocuments],
  (currentSessionId, chatSessionDocuments) => {
    if (!currentSessionId) {
      return [];
    }
    return chatSessionDocuments[currentSessionId] || [];
  }
);

export const selectAllSessionDocuments = (state: RootState) => state.chat.chatSessionDocuments;
export const selectIsLoading = (state: RootState) => state.chat.isLoading;
export const selectIsLoadingMessages = (state: RootState) => state.chat.isLoadingMessages;
export const selectIsSendingMessage = (state: RootState) => state.chat.isSendingMessage;
export const selectError = (state: RootState) => state.chat.error;

// Export reducer
export default chatSlice.reducer;
