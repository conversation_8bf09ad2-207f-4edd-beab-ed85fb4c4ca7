
import React, { useRef, useEffect } from 'react';
import { ChatMessage, Document } from '@/types/chat';
import { FileText } from 'lucide-react';


interface ChatMessagesProps {
  messages: ChatMessage[];
  documents: Document[];
}

const ChatMessages: React.FC<ChatMessagesProps> = ({ messages, documents }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const renderMessageContent = (content: string) => {
    // Check for bullet points and format them
    if (content.includes('•')) {
      const parts = content.split(/\n(?=•)/g);
      return (
        <div>
          {parts.map((part, idx) => {
            if (part.startsWith('•')) {
              return <div key={idx} className="ml-1">{part}</div>;
            }
            return <p key={idx} className="mb-2">{part}</p>;
          })}
        </div>
      );
    }

    // Check for numbered lists
    if (/^\d+\./.test(content)) {
      const parts = content.split(/\n(?=\d+\.)/g);
      return (
        <div>
          {parts.map((part, idx) => {
            if (/^\d+\./.test(part)) {
              return <div key={idx} className="ml-1">{part}</div>;
            }
            return <p key={idx} className="mb-2">{part}</p>;
          })}
        </div>
      );
    }

    // Handle paragraphs with proper spacing
    const paragraphs = content.split('\n\n');
    return (
      <div>
        {paragraphs.map((paragraph, idx) => (
          <p key={idx} className="mb-2">{paragraph}</p>
        ))}
      </div>
    );
  };

  const renderMessageSources = (message: ChatMessage) => {
    if (!message.sources) return null;

    return (
      <div className="mt-2 text-xs text-muted-foreground">
        <div className="flex items-center gap-1">
          <FileText className="h-3 w-3" />
          <span>
            Sources:{' '}
            {message.sources.map((source, idx) => {
              const doc = documents.find(d => d.id === source.documentId);
              return (
                <span key={source.documentId}>
                  {idx > 0 && ', '}
                  {doc?.name || 'Document'}
                  {source.pages && ` (p. ${source.pages.join(', ')})`}
                </span>
              );
            })}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {sortedMessages.map(message => {
        if (message.role === 'system') {
          return (
            <div key={message.id} className="flex justify-center">
              <div className="bg-muted px-4 py-2 rounded-full text-xs text-center max-w-md">
                {message.content}
              </div>
            </div>
          );
        }

        return (
          <div
            key={message.id}
            className={`flex items-start gap-3 ${
              message.role === 'user' ? 'justify-end' : ''
            }`}
          >
            {message.role === 'assistant' && (
              <div className="h-8 w-8 rounded-full bg-[#8B5CF6] flex items-center justify-center text-white font-medium text-sm flex-shrink-0">
                AI
              </div>
            )}

            <div className={`rounded-lg p-3 text-sm max-w-[80%] ${
              message.role === 'user'
                ? 'bg-[#EDE9FE] text-gray-800'
                : 'bg-gray-100'
            }`}>
              {renderMessageContent(message.content)}
              {renderMessageSources(message)}
            </div>

            {message.role === 'user' && (
              <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-medium text-sm flex-shrink-0">
                U
              </div>
            )}
          </div>
        );
      })}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessages;
