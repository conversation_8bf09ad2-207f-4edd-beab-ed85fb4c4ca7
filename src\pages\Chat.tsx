import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import Navbar from '@/components/Navbar';
import ChatMessages from '@/components/ChatMessages';
import { Document, ChatMessage } from '@/types/chat';
import EmptyDocumentState from '@/components/EmptyDocumentState';
import ChatSessionItem from '@/components/ChatSessionItem';
import DocumentViewer from '@/components/DocumentViewer';
import FileUploadWithProgress from '@/components/FileUploadWithProgress';
import ChatWithStreaming from '@/components/ChatWithStreaming';
import { useWebSocketContext } from '@/contexts/WebSocketContext';
import { useAppSelector, useAppDispatch } from '@/app/hooks';
import { selectUser } from '@/features/auth/authSlice';
import {
  fetchChatSessions,
  fetchSessionDocuments,
  fetchMessages,
  setCurrentChatSession,
  setCurrentDocument,
  addMessage,
  updateChatSession,
  removeDocumentFromSession,
  setMessages,
  sendMessage,
  createChatSession,
  updateChatSessionAPI,
  deleteChatSessionAPI,
  uploadDocumentToSession,
  selectChatSessions,
  selectCurrentChatSession,
  selectCurrentDocument,
  selectMessages,
  selectSessionDocuments,
  selectAllSessionDocuments,
  selectIsLoadingMessages,
  selectIsSendingMessage
} from '@/features/chat/chatSlice';
import { useApiLoading } from '@/hooks/useLoadingAnimation';
import { LoadingOverlay, LoadingButton } from '@/components/ui/loading-overlay';
import { TypingDotsLoader } from '@/components/ui/loading-animations';
import { FileText, Send, Plus, ChevronLeft, ChevronRight, Download } from 'lucide-react';
import { toast } from "sonner";

const Chat: React.FC = () => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const chatSessions = useAppSelector(selectChatSessions);
  const currentChatSession = useAppSelector(selectCurrentChatSession);
  const currentDocument = useAppSelector(selectCurrentDocument);
  const messages = useAppSelector(selectMessages);
  const sessionDocuments = useAppSelector(selectSessionDocuments);
  const allSessionDocuments = useAppSelector(selectAllSessionDocuments);
  const isLoadingMessages = useAppSelector(selectIsLoadingMessages);
  const isSendingMessage = useAppSelector(selectIsSendingMessage);

  // WebSocket context for real-time features
  const webSocket = useWebSocketContext();

  // Loading animations hook
  const { startLoading, stopLoading, isLoading, getLoader } = useApiLoading();

  // Local state - simplified
  const [inputValue, setInputValue] = useState('');
  const [loadingDocuments, setLoadingDocuments] = useState<Record<string, boolean>>({});
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [renameSessionId, setRenameSessionId] = useState<string | null>(null);
  const [newSessionName, setNewSessionName] = useState('');
  const [isNewChatDialogOpen, setIsNewChatDialogOpen] = useState(false);
  const [newChatSessionName, setNewChatSessionName] = useState('');
  const [showFileUploadProgress, setShowFileUploadProgress] = useState(false);
  const [sessionOperations, setSessionOperations] = useState<Record<string, {
    isEditing?: boolean;
    isDeleting?: boolean;
    isUploadingDocument?: boolean;
  }>>({});

  // Refs
  const renamePanelRef = useRef<HTMLDivElement>(null);
  const hasInitializedChat = useRef(false);

  // Get selected session from Redux
  const selectedSession = currentChatSession?.id || null;
  const selectedDocuments = currentChatSession?.documentIds || [];

  // Auto-collapse sidebar on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) { // lg breakpoint
        setIsSidebarCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Memoized handlers to prevent unnecessary re-renders
  const handleFetchDocuments = useCallback((sessionId: string, forceRefresh: boolean = false) => {
    const cachedDocuments = allSessionDocuments[sessionId];

    if (cachedDocuments && cachedDocuments.length >= 0 && !forceRefresh) {
      return;
    }

    setLoadingDocuments(prev => ({ ...prev, [sessionId]: true }));
    dispatch(fetchSessionDocuments(sessionId)).finally(() => {
      setLoadingDocuments(prev => ({ ...prev, [sessionId]: false }));
    });
  }, [dispatch, allSessionDocuments]);

  // Initialize chat sessions when component mounts
  useEffect(() => {
    const initChatSessions = async () => {
      if (!hasInitializedChat.current) {
        hasInitializedChat.current = true;
        startLoading('FETCH_SESSIONS');

        try {
          const sessions = await dispatch(fetchChatSessions()).unwrap();
          stopLoading('FETCH_SESSIONS');

          // Auto-select the latest session if available
          if (sessions.length > 0) {
            const latestSession = sessions[0];
            dispatch(setCurrentChatSession(latestSession));

            // Load documents and messages for the selected session
            handleFetchDocuments(latestSession.id);
            dispatch(fetchMessages(latestSession.id)).catch(console.warn);
          }
        } catch (error) {
          console.error('Failed to load chat sessions:', error);
          toast.error('Failed to load chat sessions');
          stopLoading('FETCH_SESSIONS');
          hasInitializedChat.current = false;
        }
      }
    };

    initChatSessions();
  }, [dispatch, startLoading, stopLoading, handleFetchDocuments]);

  const handleSessionSelect = useCallback((sessionId: string) => {
    const selectedSession = chatSessions.find(s => s && s.id === sessionId);
    if (selectedSession && selectedSession.id !== currentChatSession?.id) {
      // Clear current messages before switching
      dispatch(setMessages([]));
      dispatch(setCurrentChatSession(selectedSession));
      handleFetchDocuments(sessionId);
      // Fetch messages for the new session
      dispatch(fetchMessages(sessionId)).catch(console.warn);
    }
  }, [chatSessions, currentChatSession?.id, dispatch, handleFetchDocuments]);

  // Handle click outside of rename panel
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (renameSessionId &&
          renamePanelRef.current &&
          !renamePanelRef.current.contains(event.target as Node) &&
          !isLoading('RENAME_SESSION')) {
        handleCancelRename();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [renameSessionId, isLoading]);

  const handleDocumentUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0 || !selectedSession) {
      if (!selectedSession) toast.error('Please select a chat session first');
      return;
    }

    setSessionOperations(prev => ({
      ...prev,
      [selectedSession]: { ...prev[selectedSession], isUploadingDocument: true }
    }));

    try {
      let uploadedCount = 0;
      for (const file of Array.from(files)) {
        try {
          await dispatch(uploadDocumentToSession({ file, sessionId: selectedSession })).unwrap();
          uploadedCount++;
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error);
          toast.error(`Failed to upload ${file.name}`);
        }
      }

      if (uploadedCount > 0) {
        const systemMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'system',
          content: `${uploadedCount} new document${uploadedCount > 1 ? 's' : ''} uploaded and added to the chat.`,
          timestamp: new Date().toISOString()
        };
        dispatch(addMessage(systemMessage));
        toast.success(`${uploadedCount} document${uploadedCount > 1 ? 's' : ''} uploaded successfully`);
        handleFetchDocuments(selectedSession, true);
      }
    } finally {
      setSessionOperations(prev => ({
        ...prev,
        [selectedSession]: { ...prev[selectedSession], isUploadingDocument: false }
      }));
      e.target.value = '';
    }
  };

  // Dialog and UI handlers
  const openNewChatDialog = () => {
    setIsNewChatDialogOpen(true);
    setNewChatSessionName('');
  };

  const closeNewChatDialog = () => {
    setIsNewChatDialogOpen(false);
    setNewChatSessionName('');
  };

  const createNewSession = async () => {
    if (!newChatSessionName.trim() || isLoading('CREATE_SESSION')) return;

    startLoading('CREATE_SESSION');
    try {
      await dispatch(createChatSession(newChatSessionName.trim())).unwrap();
      dispatch(setCurrentDocument(null));
      dispatch(setMessages([]));
      closeNewChatDialog();
      document.getElementById('file-upload')?.click();
      toast.success('New chat session created');
    } catch (error) {
      console.error('Failed to create new session:', error);
      toast.error('Failed to create new session');
    } finally {
      stopLoading('CREATE_SESSION');
    }
  };

  const viewDocument = (doc: Document) => {
    dispatch(setCurrentDocument(doc));
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  // Session management handlers
  const handleEditSession = (sessionId: string) => {
    setRenameSessionId(sessionId);
    setNewSessionName(chatSessions.find(s => s.id === sessionId)?.name || '');
  };

  const handleCancelRename = () => {
    setRenameSessionId(null);
    setNewSessionName('');
  };

  const handleRenameSession = async () => {
    if (!renameSessionId || !newSessionName.trim() || isLoading('RENAME_SESSION')) return;

    startLoading('RENAME_SESSION');
    setSessionOperations(prev => ({
      ...prev,
      [renameSessionId]: { ...prev[renameSessionId], isEditing: true }
    }));

    try {
      await dispatch(updateChatSessionAPI({
        sessionId: renameSessionId,
        name: newSessionName.trim()
      })).unwrap();

      setRenameSessionId(null);
      setNewSessionName('');
      toast.success('Chat session renamed');
    } catch (error) {
      console.error('Failed to rename session:', error);
      toast.error('Failed to rename session');
    } finally {
      stopLoading('RENAME_SESSION');
      if (renameSessionId) {
        setSessionOperations(prev => ({
          ...prev,
          [renameSessionId]: { ...prev[renameSessionId], isEditing: false }
        }));
      }
    }
  };

  const handleDeleteSession = async (sessionId: string) => {
    if (!confirm('Are you sure you want to delete this chat session?')) return;

    startLoading('DELETE_SESSION');
    setSessionOperations(prev => ({
      ...prev,
      [sessionId]: { ...prev[sessionId], isDeleting: true }
    }));

    try {
      await dispatch(deleteChatSessionAPI(sessionId)).unwrap();
      if (selectedSession === sessionId) {
        dispatch(setMessages([]));
      }
      toast.success('Chat session deleted');
    } catch (error) {
      console.error('Failed to delete session:', error);
      toast.error('Failed to delete session');
    } finally {
      stopLoading('DELETE_SESSION');
      setSessionOperations(prev => ({
        ...prev,
        [sessionId]: { ...prev[sessionId], isDeleting: false }
      }));
    }
  };

  const handleAddDocumentToSession = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session) {
      dispatch(setCurrentChatSession(session));
    }
    document.getElementById('file-upload')?.click();
  };

  const handleDeleteDocument = async (docId: string) => {
    if (!selectedSession) return;

    try {
      dispatch(removeDocumentFromSession({ sessionId: selectedSession, documentId: docId }));

      if (currentDocument && currentDocument.id === docId) {
        dispatch(setCurrentDocument(null));
      }

      const systemMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'system',
        content: 'Document removed from the chat.',
        timestamp: new Date().toISOString()
      };
      dispatch(addMessage(systemMessage));
      toast.success('Document removed from chat');
    } catch (error) {
      console.error('Failed to remove document:', error);
      toast.error('Failed to remove document');
    }
  };

  // Message handling functions
  const handleSendMessage = async () => {
    if (!inputValue.trim() || !selectedSession || isSendingMessage) return;

    const messageContent = inputValue.trim();
    setInputValue('');

    try {
      const userMessage: ChatMessage = {
        id: `msg-${Date.now()}`,
        role: 'user',
        content: messageContent,
        timestamp: new Date().toISOString()
      };
      dispatch(addMessage(userMessage));

      await dispatch(sendMessage({
        sessionId: selectedSession,
        message: messageContent,
        chatHistory: messages,
        useAgent: false
      })).unwrap();

      if (selectedSession) {
        const updatedSession = chatSessions.find(s => s.id === selectedSession);
        if (updatedSession) {
          dispatch(updateChatSession({
            ...updatedSession,
            lastMessageTime: new Date().toISOString()
          }));
        }
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Computed values
  const currentSessionDocuments = selectedSession && sessionDocuments.length > 0
    ? sessionDocuments
    : [];

  const renderDocumentPreview = () => {
    if (!currentDocument) return null;
    return <DocumentViewer document={currentDocument} />;
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-1 flex mt-16">
        {/* Hidden file upload input */}
        <input
          id="file-upload"
          type="file"
          multiple
          className="hidden"
          onChange={handleDocumentUpload}
        />

        {/* Rename Session Panel */}
        {renameSessionId && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div ref={renamePanelRef} className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium mb-4">Rename Chat Session</h3>
              <input
                type="text"
                value={newSessionName}
                onChange={(e) => setNewSessionName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && newSessionName.trim() && !isLoading('RENAME_SESSION')) {
                    handleRenameSession();
                  } else if (e.key === 'Escape') {
                    handleCancelRename();
                  }
                }}
                className="w-full p-2 border rounded mb-4 focus:outline-none focus:ring-2 focus:ring-[#8B5CF6]"
                placeholder="Enter new name"
                disabled={isLoading('RENAME_SESSION')}
                autoFocus
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={handleCancelRename}
                  disabled={isLoading('RENAME_SESSION')}
                >
                  Cancel
                </Button>
                <LoadingButton
                  isLoading={isLoading('RENAME_SESSION')}
                  loadingType="pulse-dots"
                  loadingText="Renaming..."
                  className="bg-[#8B5CF6] hover:bg-[#7C3AED]"
                  onClick={handleRenameSession}
                  disabled={!newSessionName.trim()}
                >
                  Rename
                </LoadingButton>
              </div>
            </div>
          </div>
        )}

        {/* New Chat Session Dialog */}
        {isNewChatDialogOpen && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium mb-4">Create New Chat Session</h3>
              <input
                type="text"
                value={newChatSessionName}
                onChange={(e) => setNewChatSessionName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && newChatSessionName.trim() && !isLoading('CREATE_SESSION')) {
                    createNewSession();
                  } else if (e.key === 'Escape') {
                    closeNewChatDialog();
                  }
                }}
                className="w-full p-2 border rounded mb-4 focus:outline-none focus:ring-2 focus:ring-[#8B5CF6]"
                placeholder="Enter session name"
                disabled={isLoading('CREATE_SESSION')}
                autoFocus
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={closeNewChatDialog}
                  disabled={isLoading('CREATE_SESSION')}
                >
                  Cancel
                </Button>
                <LoadingButton
                  isLoading={isLoading('CREATE_SESSION')}
                  loadingType="rotating-hexagon"
                  loadingText="Creating..."
                  className="bg-[#8B5CF6] hover:bg-[#7C3AED]"
                  onClick={createNewSession}
                  disabled={!newChatSessionName.trim()}
                >
                  Create
                </LoadingButton>
              </div>
            </div>
          </div>
        )}

        {/* Mobile overlay when sidebar is open */}
        {!isSidebarCollapsed && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setIsSidebarCollapsed(true)}
          />
        )}

        {/* Sidebar - Chat Sessions */}
        <div className={`${isSidebarCollapsed ? 'w-0' : 'w-[240px] min-w-[240px]'} border-r flex flex-col bg-[#f8f9fa] transition-all duration-300 overflow-hidden h-[calc(100vh-64px)] relative z-50 lg:z-auto`}>
          {/* New chat button */}
          <div className="p-3 flex-shrink-0">
            <LoadingButton
              isLoading={isLoading('CREATE_SESSION')}
              loadingType="rotating-hexagon"
              loadingText="Creating..."
              className="w-full bg-[#8B5CF6] hover:bg-[#7C3AED] flex gap-2"
              onClick={openNewChatDialog}
              disabled={isLoading('CREATE_SESSION')}
            >
              <Plus className="h-4 w-4" />
              <span>New Chat</span>
            </LoadingButton>
          </div>

          {/* Chat sessions list with scrollbar */}
          <div className="flex-1 overflow-y-auto p-2 min-h-0 chat-sessions-scroll">
            {isLoading('FETCH_SESSIONS') ? (
              <div className="flex flex-col items-center justify-center py-8 space-y-4">
                {getLoader('FETCH_SESSIONS')}
                <p className="text-sm text-gray-500">Loading chat sessions...</p>
              </div>
            ) : (
              <div className="space-y-1">
                {chatSessions.filter(session => session && session.id).map(session => (
                  <ChatSessionItem
                    key={session.id}
                    session={session}
                    isSelected={selectedSession === session.id}
                    documents={allSessionDocuments[session.id] || []}
                    selectedDocuments={selectedDocuments}
                    currentDocumentId={currentDocument?.id}
                    onSessionSelect={handleSessionSelect}
                    onDocumentView={viewDocument}
                    onEditSession={handleEditSession}
                    onDeleteSession={handleDeleteSession}
                    onAddDocumentToSession={handleAddDocumentToSession}
                    onDocumentDelete={handleDeleteDocument}
                    onFetchDocuments={handleFetchDocuments}
                    isLoadingDocuments={loadingDocuments[session.id] || false}
                    isEditingSession={sessionOperations[session.id]?.isEditing || false}
                    isDeletingSession={sessionOperations[session.id]?.isDeleting || false}
                    isUploadingDocument={sessionOperations[session.id]?.isUploadingDocument || false}
                  />
                ))}
              </div>
            )}
          </div>

          {/* User profile - always visible at bottom */}
          <div className="p-3 border-t bg-[#f8f9fa] flex-shrink-0">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-slate-800 flex items-center justify-center text-white text-sm font-medium">
                {user?.name?.charAt(0) || user?.full_name?.charAt(0) || 'U'}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">
                  {user?.name || user?.full_name || 'User'}
                </div>
                <div className="text-xs text-gray-500 truncate">
                  {user?.email || '<EMAIL>'}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Toggle sidebar button - moved to document header */}
        <div className="hidden">
          {/* This is just to keep the imports */}
          <ChevronRight />
          <ChevronLeft />
        </div>

        {/* Main Content Area */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-[60fr_40fr] h-[calc(100vh-64px)]">
          {/* Document Viewer Section */}
          <div className="flex flex-col border-r overflow-hidden">
            {/* Document Header */}
            <div className="p-3 border-b flex items-center justify-between bg-white">
              <div className="flex items-center gap-3">
                <button
                  onClick={toggleSidebar}
                  className="p-1 rounded-md hover:bg-gray-100 transition-colors"
                  aria-label={isSidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                >
                  {isSidebarCollapsed ?
                    <ChevronRight className="h-4 w-4 text-gray-500" /> :
                    <ChevronLeft className="h-4 w-4 text-gray-500" />
                  }
                </button>
                <h2 className="font-semibold truncate text-gray-800">
                  {currentDocument ? currentDocument.name : 'Select a document'}
                </h2>
              </div>
              {currentDocument?.url && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-500 hover:text-gray-700 flex-shrink-0"
                  onClick={() => window.open(currentDocument.url, '_blank')}
                  title="Download document"
                >
                  <Download className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Document Content */}
            <div className="flex-1 overflow-hidden">
              {currentDocument ? (
                renderDocumentPreview()
              ) : (
                <div className="flex-1 flex items-center justify-center p-8 text-muted-foreground">
                  <div className="text-center max-w-sm">
                    <FileText className="h-16 w-16 mx-auto mb-4 opacity-40" />
                    <h3 className="text-lg font-medium mb-2 text-gray-700">No document selected</h3>
                    <p className="text-sm text-gray-500">
                      {currentChatSession
                        ? 'Select a document from the session to view its contents'
                        : 'Choose a chat session to see its documents'
                      }
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Chat Section */}
          <div className="flex flex-col bg-white overflow-hidden">
            {/* Chat Header */}
            <div className="p-4 border-b flex items-center justify-between bg-white">
              <h2 className="font-semibold text-gray-800">
                {selectedSession
                  ? chatSessions.find(s => s && s.id === selectedSession)?.name || 'New Chat'
                  : 'Select or create a chat'}
              </h2>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>Real-time streaming</span>
                <div className={`w-2 h-2 rounded-full ${webSocket.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              </div>
            </div>

            {/* Chat Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {!selectedSession ? (
                <div className="h-full flex flex-col items-center justify-center text-center p-8">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                    <FileText className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 text-gray-800">Select or create a chat</h3>
                  <p className="text-gray-600 mb-6">Choose an existing chat or start a new one</p>
                  <Button
                    className="bg-[#8B5CF6] hover:bg-[#7C3AED] flex gap-2"
                    onClick={openNewChatDialog}
                  >
                    <Plus className="h-4 w-4" />
                    <span>New Chat</span>
                  </Button>
                </div>
              ) : currentSessionDocuments.length === 0 ? (
                <div className="flex-1 flex flex-col">
                  <div className="flex-1 flex items-center justify-center">
                    <EmptyDocumentState onAddDocuments={() => setShowFileUploadProgress(true)} />
                  </div>
                  {showFileUploadProgress && (
                    <div className="border-t p-4">
                      <FileUploadWithProgress
                        sessionId={selectedSession}
                        onUploadComplete={(fileId, fileStatus) => {
                          console.log('File uploaded:', fileId, fileStatus);
                          if (selectedSession) {
                            handleFetchDocuments(selectedSession, true);
                          }
                          setShowFileUploadProgress(false);
                        }}
                      />
                    </div>
                  )}
                </div>
              ) : webSocket.isConnected ? (
                <ChatWithStreaming
                  sessionId={selectedSession}
                  documents={currentSessionDocuments.map(doc => ({
                    id: doc.id,
                    filename: doc.name,
                    status: 'processed'
                  }))}
                  className="flex-1"
                />
              ) : (
                <div className="flex-1 flex flex-col min-h-0">
                  {/* Messages Area */}
                  <div className="flex-1 overflow-y-auto p-4 min-h-0" style={{ maxHeight: 'calc(100vh - 200px)' }}>
                    {messages.length === 0 ? (
                      <div className="h-full flex flex-col items-center justify-center text-center p-6">
                        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                          <FileText className="h-8 w-8 text-purple-600" />
                        </div>
                        <h3 className="text-xl font-semibold mb-2 text-gray-800">Start a new conversation</h3>
                        <p className="text-gray-600 mb-6">Ask me anything about your documents</p>
                        <div className="grid grid-cols-2 gap-3 w-full max-w-sm">
                          <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
                            Summarize main topics
                          </Button>
                          <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
                            Explain key concepts
                          </Button>
                          <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
                            Important findings
                          </Button>
                          <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
                            Bullet point summary
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="min-h-full">
                        <ChatMessages messages={messages} documents={sessionDocuments} />
                        {isSendingMessage && (
                          <div className="flex justify-start mb-4">
                            <TypingDotsLoader className="max-w-xs" />
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Message Input */}
                  <div className="border-t p-4 bg-gray-50">
                    <div className="relative">
                      <Textarea
                        placeholder="Message AnyDocAI..."
                        className="resize-none pr-12 min-h-[60px] max-h-[200px] border-gray-200 bg-white"
                        rows={1}
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyDown={handleKeyPress}
                        disabled={isSendingMessage}
                      />
                      <Button
                        size="sm"
                        className="absolute right-2 bottom-2 rounded-full w-8 h-8 p-0 bg-[#8B5CF6] hover:bg-[#7C3AED]"
                        onClick={handleSendMessage}
                        disabled={!inputValue.trim() || isSendingMessage}
                      >
                        {isSendingMessage ? (
                          <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <Send className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Loading Overlays for major operations */}
      <LoadingOverlay
        isVisible={isLoading('PROCESS_DOCUMENT')}
        type="dna-helix"
        message="Processing document..."
        backdrop="blur"
        size="lg"
      />

      <LoadingOverlay
        isVisible={isLoading('UPLOAD_DOCUMENT')}
        type="progress-ring"
        message="Uploading document..."
        backdrop="blur"
        size="md"
      />
    </div>
  );
};

export default Chat;
