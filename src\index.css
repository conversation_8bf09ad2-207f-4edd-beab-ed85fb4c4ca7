
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 91% 60%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 262 83% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 91% 60%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 262 83% 25%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }
}

html {
  font-family: 'Inter', sans-serif;
  scroll-behavior: smooth;
}

.font-display {
  font-family: 'Playfair Display', serif;
}

/* Custom gradient text */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-docai-purple to-docai-blue;
}

/* Animation utilities */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Custom scrollbar styles */
.chat-sessions-scroll, .chat-messages-scroll {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.chat-sessions-scroll::-webkit-scrollbar, .chat-messages-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-sessions-scroll::-webkit-scrollbar-track, .chat-messages-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.chat-sessions-scroll::-webkit-scrollbar-thumb, .chat-messages-scroll::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.chat-sessions-scroll::-webkit-scrollbar-thumb:hover, .chat-messages-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* Chat messages specific scrolling */
.chat-messages-container {
  height: calc(100vh - 280px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Loading Animation Keyframes */
@keyframes float-0 {
  0%, 100% { transform: rotate(0deg) translateX(20px) scale(1); opacity: 0.7; }
  50% { transform: rotate(0deg) translateX(25px) scale(1.2); opacity: 1; }
}

@keyframes float-1 {
  0%, 100% { transform: rotate(60deg) translateX(20px) scale(1); opacity: 0.7; }
  50% { transform: rotate(60deg) translateX(25px) scale(1.2); opacity: 1; }
}

@keyframes float-2 {
  0%, 100% { transform: rotate(120deg) translateX(20px) scale(1); opacity: 0.7; }
  50% { transform: rotate(120deg) translateX(25px) scale(1.2); opacity: 1; }
}

@keyframes float-3 {
  0%, 100% { transform: rotate(180deg) translateX(20px) scale(1); opacity: 0.7; }
  50% { transform: rotate(180deg) translateX(25px) scale(1.2); opacity: 1; }
}

@keyframes float-4 {
  0%, 100% { transform: rotate(240deg) translateX(20px) scale(1); opacity: 0.7; }
  50% { transform: rotate(240deg) translateX(25px) scale(1.2); opacity: 1; }
}

@keyframes float-5 {
  0%, 100% { transform: rotate(300deg) translateX(20px) scale(1); opacity: 0.7; }
  50% { transform: rotate(300deg) translateX(25px) scale(1.2); opacity: 1; }
}

@keyframes helix-0 {
  0% { transform: translateX(-50%) rotateY(0deg) translateX(15px); }
  100% { transform: translateX(-50%) rotateY(360deg) translateX(15px); }
}

@keyframes helix-1 {
  0% { transform: translateX(-50%) rotateY(180deg) translateX(15px); }
  100% { transform: translateX(-50%) rotateY(540deg) translateX(15px); }
}
